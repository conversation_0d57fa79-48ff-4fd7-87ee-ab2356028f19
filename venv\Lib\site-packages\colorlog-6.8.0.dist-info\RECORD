colorlog-6.8.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
colorlog-6.8.0.dist-info/LICENSE,sha256=sdkIK8SDYj_Vn8cnm0V_DkDZQqdkJs3iVyOeBN_kElo,1107
colorlog-6.8.0.dist-info/METADATA,sha256=Ur1tpH5PuMUO4PN9QqOaiJSN_HT8qexXR01B9rZ99OI,10713
colorlog-6.8.0.dist-info/RECORD,,
colorlog-6.8.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
colorlog-6.8.0.dist-info/WHEEL,sha256=oiQVh_5PnQM0E3gPdiz09WCNmwiHDMaGer_elqB3coM,92
colorlog-6.8.0.dist-info/top_level.txt,sha256=CzNs7OLwLxUhbQzCCT2ore3b_ZzAXusw0tWIX79iWow,9
colorlog/__init__.py,sha256=wzxah0vO2HpJheG0gXY4rMx_MyFYyfHsR8rTucg5PSI,1180
colorlog/__pycache__/__init__.cpython-312.pyc,,
colorlog/__pycache__/escape_codes.cpython-312.pyc,,
colorlog/__pycache__/formatter.cpython-312.pyc,,
colorlog/__pycache__/wrappers.cpython-312.pyc,,
colorlog/escape_codes.py,sha256=jFxDvDyWHk5gX5tHpFIMF5om52dI_jCqyTBmeMtnpxQ,2437
colorlog/formatter.py,sha256=ptHZw4Ulq5G-XBlV9DO4B7PIONBAoHL-1YAjIdQbqyk,7741
colorlog/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
colorlog/wrappers.py,sha256=yhRxU7k4-4v0ooe8UNcyKn8x5SCEUGs4um_ukczNCiQ,2202
